# MagicBricks Scraper - Git Ignore File

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Scraper-specific files
output/*.csv
output/*.json
output/*.log
output/scraper_checkpoint_*.json
temp_config.json

# Browser and WebDriver
chromedriver.exe
geckodriver.exe
*.log

# Sensitive data
config/secrets.json
config/private_*.json
*.key
*.pem

# Large data files
*.zip
*.tar.gz
*.rar

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Jupyter Notebooks
.ipynb_checkpoints

# Keep output directory but ignore contents
output/.gitkeep

# Keep important config files
!config/scraper_config.json
!config/example_*.json
