# 🚀 MagicBricks Scraper - Quick Start Guide

## 📁 **CLEAN PROJECT STRUCTURE**

```
MagicB<PERSON>/
├── 📄 integrated_magicbricks_scraper.py    # Main scraper engine
├── 🖥️ magicbricks_gui.py                   # GUI interface
├── ⚙️ user_mode_options.py                 # Scraping modes
├── 🔄 incremental_scraping_system.py       # Incremental logic
├── 📅 date_parsing_system.py               # Date processing
├── 🛑 smart_stopping_logic.py              # Smart stopping
├── 🔗 url_tracking_system.py               # URL management
├── ❌ error_handling_system.py             # Error handling
├── 🗃️ incremental_database_schema.py       # Database setup
├── 📊 enhanced_data_schema.py              # Data schema
├── 📋 requirements.txt                     # Dependencies
├── 📖 README.md                           # Main documentation
├── 👤 USER_MANUAL.md                      # User guide
├── 🗄️ magicbricks_enhanced.db             # Database file
├── 📝 integrated_scraper.log              # Current log
├── 📁 config/                             # Configuration files
├── 📁 data/                               # Data files
└── 📁 archive/                            # Archived files
```

## 🎯 **HOW TO RUN THE SCRAPER**

### **Method 1: GUI Interface (Recommended)**

1. **Open Command Prompt/Terminal**
   ```bash
   cd "D:/real estate/Scrapers/Magicbricks"
   ```

2. **Launch GUI**
   ```bash
   python magicbricks_gui.py
   ```

3. **Configure Settings**
   - Select city from dropdown
   - Choose scraping mode (Incremental/Full/Conservative)
   - Set maximum pages
   - Configure advanced settings if needed

4. **Start Scraping**
   - Click "Start Scraping" button
   - Monitor progress in real-time
   - Wait for completion

### **Method 2: Command Line**

```python
from integrated_magicbricks_scraper import IntegratedMagicBricksScraper
from user_mode_options import ScrapingMode

# Create scraper instance
scraper = IntegratedMagicBricksScraper()

# Run scraping
result = scraper.scrape_properties_with_incremental(
    city='mumbai',
    mode=ScrapingMode.INCREMENTAL,
    max_pages=5,
    export_formats=['csv', 'json', 'excel']
)
```

## 📊 **FINDING YOUR OUTPUT FILES**

### **File Naming Convention**
```
magicbricks_{mode}_scrape_{timestamp}.{format}
```

**Examples:**
- `magicbricks_full_scrape_20250811_104531.csv`
- `magicbricks_full_scrape_20250811_104531.json`
- `magicbricks_full_scrape_20250811_104531.xlsx`

### **How to Identify Latest Files**

1. **By Timestamp in Filename**
   - Format: `YYYYMMDD_HHMMSS`
   - Latest = Highest timestamp
   - Example: `20250811_104531` = Aug 11, 2025, 10:45:31

2. **By File Creation Date**
   - Right-click file → Properties → Date Created
   - Sort by "Date Modified" in File Explorer

3. **In GUI**
   - Success message shows exact filename
   - Files are created in main project directory

### **Output File Locations**
- **CSV Files**: Main directory (`magicbricks_*.csv`)
- **JSON Files**: Main directory (`magicbricks_*.json`)
- **Excel Files**: Main directory (`magicbricks_*.xlsx`)
- **Database**: `magicbricks_enhanced.db`
- **Logs**: `integrated_scraper.log`

## 🔧 **ADVANCED FEATURES**

### **Export Formats**
- ✅ **CSV** (Mandatory): Standard spreadsheet format
- ✅ **Database** (Mandatory): SQLite database
- 📄 **JSON** (Optional): Structured data format
- 📊 **Excel** (Optional): Multi-sheet workbook with summary

### **Filtering Options**
- 💰 **Price Range**: Filter by price (in Lakhs)
- 📐 **Area Range**: Filter by area (Sq.Ft)
- 🏠 **BHK Types**: 1, 2, 3, 4, 4+ BHK
- 🚫 **Exclude Keywords**: Filter out unwanted properties

### **Performance Settings**
- ⏱️ **Custom Delays**: Adjust scraping speed
- 🔄 **Batch Processing**: Configure batch sizes
- 💾 **Memory Optimization**: Enable for large datasets
- 🔁 **Retry Logic**: Configure retry attempts

## 📈 **UNDERSTANDING OUTPUT DATA**

### **CSV/Excel Columns**
- `title`: Property title
- `price`: Property price
- `area`: Property area
- `property_url`: Direct link to property
- `locality`: Location area
- `society`: Building/society name
- `property_type`: Apartment/House/Plot
- `bathrooms`: Number of bathrooms
- `balcony`: Number of balconies
- `furnishing`: Furnished/Semi/Unfurnished
- `floor_details`: Floor information
- `facing`: Property facing direction
- `parking`: Parking availability
- `ownership`: Ownership type
- `posting_date_text`: When posted
- `data_quality_score`: Quality score (0-100%)

### **JSON Structure**
```json
{
  "metadata": {
    "scrape_timestamp": "2025-08-11T10:45:31",
    "total_properties": 150,
    "session_stats": {...}
  },
  "properties": [...]
}
```

### **Excel Sheets**
1. **Properties**: All property data
2. **Summary**: Scraping statistics
3. **City_Stats**: City-specific metrics

## 🚨 **TROUBLESHOOTING**

### **Common Issues**
1. **"No module named..." Error**
   ```bash
   pip install -r requirements.txt
   ```

2. **GUI Won't Start**
   - Check Python version (3.8+)
   - Install tkinter: `pip install tk`

3. **No Properties Found**
   - Check internet connection
   - Try different city
   - Disable filtering

4. **Slow Performance**
   - Reduce max pages
   - Disable individual page scraping
   - Increase delays in advanced settings

### **Getting Help**
- Check `integrated_scraper.log` for errors
- Review `USER_MANUAL.md` for detailed instructions
- Ensure all dependencies are installed

## 🎉 **SUCCESS INDICATORS**

✅ **Successful Run:**
- GUI shows "Scraping completed successfully"
- Output files created with timestamp
- Log shows no critical errors
- Properties count > 0

✅ **Quality Data:**
- Data quality score > 80%
- Most essential fields filled
- Valid URLs and prices
- Recent posting dates

---

**🚀 Ready to scrape! Start with the GUI for the best experience.**
